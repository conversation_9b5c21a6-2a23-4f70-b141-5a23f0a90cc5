import { Router, Request, Response } from 'express';
import { ChatService } from '../services/chatService.js';
import { ConversationService } from '../services/conversationService.js';

const router = Router();

// Text-based chat endpoint
router.post('/chat', async (req: Request, res: Response) => {
  try {
    const { messages, format, conversationId, assumptionMode } = req.body;
    const result = await ChatService.processChat(messages, format, conversationId, assumptionMode);
    res.json(result);
  } catch (error: unknown) {
    console.error('Chat API Error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ 
      error: 'Failed to process chat request',
      details: errorMessage 
    });
  }
});

// Follow-up chat endpoint
router.post('/followup', async (req: Request, res: Response) => {
  try {
    const { messages, conversationId, assumptionMode } = req.body;
    const result = await ChatService.processFollowup(messages, conversationId, assumptionMode);
    res.json(result);
  } catch (error: unknown) {
    console.error('Follow-up API Error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ 
      error: 'Failed to process follow-up request',
      details: errorMessage 
    });
  }
});

// Streaming chat endpoint
router.post('/chat/stream', async (req: Request, res: Response) => {
  try {
    const { messages, format, conversationId, assumptionMode } = req.body;

    // Set up SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    const responseData = await ChatService.processChatStream(messages, format, conversationId, assumptionMode);

    // Send initial metadata
    res.write(`data: ${JSON.stringify({
      type: 'metadata',
      conversationId: responseData.conversationId,
      shouldShowEndPrompt: responseData.conversationState.shouldShowEndPrompt,
      userResponseCount: responseData.conversationState.userResponseCount
    })}\n\n`);

    // Simulate streaming by sending the response in chunks
    const fullResponse = responseData.response;
    const words = fullResponse.split(' ');
    let tokenCount = 0;

    for (let i = 0; i < words.length; i++) {
      const word = words[i];
      const isLastWord = i === words.length - 1;
      const wordWithSpace = word + (isLastWord ? '' : ' ');

      tokenCount++;

      // Send content chunk
      res.write(`data: ${JSON.stringify({
        type: 'content',
        content: wordWithSpace,
        tokenCount: tokenCount
      })}\n\n`);

      // Add realistic delay between words
      const delay = Math.min(30 + word.length * 8, 120);
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    // Add assistant response to conversation history
    ConversationService.addMessage(responseData.conversationId, {
      role: 'assistant',
      content: fullResponse
    });

    // Generate quote data if needed
    let quoteData = null;
    if (responseData.conversationState.shouldAutoGenerateQuote) {
      // Import the quote generation function
      const { generateProjectQuote } = await import('../services/chatService.js');
      quoteData = generateProjectQuote(responseData.conversationHistory, format || 'web');
      if (quoteData) {
        quoteData.bestOption = 'aiDriven';
        quoteData.bestOptionReason = 'AI-driven development provides the best value with significant cost savings and faster delivery timeline.';
      }
    }

    // Send completion event
    res.write(`data: ${JSON.stringify({
      type: 'complete',
      shouldAutoGenerateQuote: responseData.conversationState.shouldAutoGenerateQuote,
      quoteData: quoteData,
      totalTokens: tokenCount
    })}\n\n`);

    res.end();

  } catch (error: unknown) {
    console.error('Streaming Chat API Error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    // Send error event
    res.write(`data: ${JSON.stringify({
      type: 'error',
      error: 'Failed to process streaming chat request',
      details: errorMessage
    })}\n\n`);

    res.end();
  }
});

// Streaming follow-up endpoint
router.post('/followup/stream', async (req: Request, res: Response) => {
  try {
    const { messages, conversationId, assumptionMode } = req.body;

    // Set up SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    const responseData = await ChatService.processFollowupStream(messages, conversationId, assumptionMode);

    // Send initial metadata
    res.write(`data: ${JSON.stringify({
      type: 'metadata',
      conversationId: responseData.conversationId,
      shouldShowEndPrompt: responseData.conversationState.shouldShowEndPrompt,
      userResponseCount: responseData.conversationState.userResponseCount
    })}\n\n`);

    // Simulate streaming by sending the response in chunks
    const fullResponse = responseData.response;
    const words = fullResponse.split(' ');
    let tokenCount = 0;

    for (let i = 0; i < words.length; i++) {
      const word = words[i];
      const isLastWord = i === words.length - 1;
      const wordWithSpace = word + (isLastWord ? '' : ' ');

      tokenCount++;

      // Send content chunk
      res.write(`data: ${JSON.stringify({
        type: 'content',
        content: wordWithSpace,
        tokenCount: tokenCount
      })}\n\n`);

      // Add realistic delay between words
      const delay = Math.min(30 + word.length * 8, 120);
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    // Add assistant response to conversation history
    ConversationService.addMessage(responseData.conversationId, {
      role: 'assistant',
      content: fullResponse
    });

    // Generate quote data if needed
    let quoteData = null;
    if (responseData.conversationState.shouldAutoGenerateQuote) {
      // Import the quote generation function
      const { generateProjectQuote } = await import('../services/chatService.js');
      quoteData = generateProjectQuote(responseData.conversationHistory, 'web');
      if (quoteData) {
        quoteData.bestOption = 'aiDriven';
        quoteData.bestOptionReason = 'AI-driven development provides the best value with significant cost savings and faster delivery timeline.';
      }
    }

    // Send completion event
    res.write(`data: ${JSON.stringify({
      type: 'complete',
      shouldAutoGenerateQuote: responseData.conversationState.shouldAutoGenerateQuote,
      quoteData: quoteData,
      totalTokens: tokenCount
    })}\n\n`);

    res.end();

  } catch (error: unknown) {
    console.error('Streaming Follow-up API Error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    // Send error event
    res.write(`data: ${JSON.stringify({
      type: 'error',
      error: 'Failed to process streaming follow-up request',
      details: errorMessage
    })}\n\n`);

    res.end();
  }
});

export default router;
