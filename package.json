{"name": "ai-chatbot-monorepo", "private": true, "version": "1.0.0", "description": "AI Chatbot with Node.js backend and React TypeScript frontend", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "vite", "dev:backend": "cd backend && npm run dev", "install:backend": "cd backend && npm install", "build": "vite build", "preview": "vite preview", "type-check": "tsc --noEmit", "set:credentials": "cp ~/.aws/projects/internal ~/.aws/credentials", "deploy:frontend": "npm run set:credentials; ./scripts/deployment/frontend/deploy-frontend.sh", "deploy:frontend:update": "npm run set:credentials; ./scripts/deployment/frontend/update-frontend.sh", "deploy:frontend:status": "npm run set:credentials; ./scripts/deployment/frontend/frontend-status.sh", "deploy:backend": "npm run set:credentials; ./scripts/deployment/backend/deploy-backend.sh", "deploy:backend:update": "npm run set:credentials; ./scripts/deployment/backend/update-backend.sh", "deploy:backend:status": "npm run set:credentials; ./scripts/deployment/backend/backend-status.sh", "deploy:full": "npm run set:credentials; npm run deploy:frontend && npm run deploy:backend", "status:all": "npm run set:credentials; npm run deploy:frontend:status && npm run deploy:backend:status", "docs:add": "npm run set:credentials; ./scripts/deployment/backend/add-simple-api-docs.sh", "docs:view": "npm run set:credentials; ./scripts/deployment/backend/view-api-documentation.sh", "connect:backend": "npm run set:credentials; ./scripts/connect/backend/connect-backend.sh", "connect:local": "npm run set:credentials; ./scripts/connect/backend/connect-backend.sh local", "connect:deployed": "npm run set:credentials; ./scripts/connect/backend/connect-backend.sh deployed", "connect:start": "npm run set:credentials; ./scripts/connect/backend/connect-backend.sh start", "test:backend": "node scripts/connect/backend/test-backend-connection.js", "update:lambda-env": "npm run set:credentials; ./scripts/connect/backend/update-lambda-env.sh", "dev:local": "echo 'Using localhost API' && vite", "dev:deployed": "echo 'Using deployed API' && VITE_API_BASE_URL=https://5dacn8xif1.execute-api.us-east-1.amazonaws.com/prod/api vite", "api:local": "npm run set:credentials; ./scripts/switch-api.sh local", "api:deployed": "npm run set:credentials; ./scripts/switch-api.sh deployed", "api:status": "npm run set:credentials; ./scripts/switch-api.sh status", "logs": "npm run set:credentials; ./scripts/tail-logs.sh", "logs:recent": "npm run set:credentials; ./scripts/tail-logs.sh -r", "logs:errors": "npm run set:credentials; ./scripts/tail-logs.sh -p 'ERROR'", "logs:streams": "npm run set:credentials; ./scripts/tail-logs.sh -s"}, "dependencies": {"lucide-react": "^0.525.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/node": "^20.19.10", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "concurrently": "^8.2.2", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "sass": "^1.89.2", "typescript": "^5.2.2", "vite": "^7.0.4"}}