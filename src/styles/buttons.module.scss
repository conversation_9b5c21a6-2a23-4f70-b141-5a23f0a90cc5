@use 'variables' as *;

/* Button styles with Dark Glass Effects */

.btn {
  position: relative;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-normal);
  border: none;
  font-family: inherit;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  text-decoration: none;
  overflow: hidden;
  @include glass-effect(0, 10px);
  @include gradient-border(linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent));
  @include shimmer-effect();
  @include focus-ring();
  @include disabled-state();

  &::before {
    opacity: 0;
    transition: opacity var(--transition-normal);
  }

  &:hover::after {
    transform: translateX(100%);
  }
}

/* Button variants */
.btnOutline {
  composes: btn;
  @include glass-effect(0.05);
  @include glass-border();
  color: var(--text-secondary);
  box-shadow: 
    var(--shadow-md),
    var(--shadow-glass);

  &::before {
    background: linear-gradient(
      45deg,
      rgba(255, 255, 255, 0.1),
      rgba(255, 255, 255, 0.3),
      rgba(255, 255, 255, 0.1)
    );
  }

  &:hover:not(:disabled) {
    border-color: var(--border-secondary);
    @include glass-effect(0.08);
    @include hover-lift();
    box-shadow: 
      var(--shadow-lg),
      var(--shadow-glass-hover);

    &::before {
      opacity: 1;
    }
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }
}

.btnPrimary {
  composes: btn;
  background: var(--gradient-primary);
  color: #000;
  border: 1px solid rgba(255, 206, 39, 0.3);
  box-shadow: 
    0 4px 12px rgba(255, 206, 39, 0.3),
    var(--shadow-glass);
  font-weight: var(--font-weight-semibold);

  &::before {
    background: var(--gradient-rainbow);
    background-size: 300% 300%;
    animation: gradientShift var(--animation-very-slow) ease infinite;
  }

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--color-primary-dark), var(--color-primary-darker));
    @include hover-lift();
    box-shadow: 
      0 8px 20px rgba(255, 206, 39, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);

    &::before {
      opacity: 1;
      animation-duration: var(--animation-fast);
    }
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 
      0 4px 12px rgba(255, 206, 39, 0.3),
      var(--shadow-glass);
  }

  @include focus-ring(var(--color-primary));
}

.btnSecondary {
  composes: btn;
  @include glass-effect(0.08);
  @include glass-border(0.2);
  background: linear-gradient(135deg, rgba(14, 165, 233, 0.1), rgba(58, 134, 255, 0.1));
  color: var(--text-primary);
  border-color: rgba(14, 165, 233, 0.3);
  box-shadow:
    var(--shadow-md),
    0 4px 12px rgba(14, 165, 233, 0.2);
  font-weight: var(--font-weight-semibold);

  &::before {
    background: linear-gradient(
      45deg,
      rgba(14, 165, 233, 0.2),
      rgba(58, 134, 255, 0.2),
      rgba(14, 165, 233, 0.2)
    );
  }

  &:hover:not(:disabled) {
    @include glass-effect(0.12);
    border-color: rgba(14, 165, 233, 0.5);
    @include hover-lift();
    box-shadow:
      var(--shadow-lg),
      0 8px 20px rgba(14, 165, 233, 0.3);

    &::before {
      opacity: 1;
    }
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }

  @include focus-ring(var(--color-secondary));
}

.btnFormat {
  composes: btn;
  @include glass-effect(0.03);
  @include glass-border(0.15);
  color: var(--text-tertiary);
  min-width: 80px;
  box-shadow: 
    var(--shadow-sm),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);

  &::before {
    background: linear-gradient(
      90deg,
      rgba(14, 165, 233, 0.3),
      rgba(58, 134, 255, 0.3),
      rgba(14, 165, 233, 0.3)
    );
  }

  &:hover:not(:disabled) {
    border-color: rgba(255, 255, 255, 0.25);
    @include glass-effect(0.06);
    color: rgba(255, 255, 255, 0.95);
    transform: translateY(-1px);

    &::before {
      opacity: 1;
    }
  }

  &.active {
    background: rgba(14, 165, 233, 0.2);
    border-color: rgba(14, 165, 233, 0.4);
    color: var(--text-primary);
    font-weight: var(--font-weight-semibold);
    box-shadow: 
      0 4px 12px rgba(14, 165, 233, 0.3),
      var(--shadow-glass);

    &::before {
      opacity: 1;
      background: linear-gradient(
        90deg,
        var(--color-secondary),
        var(--color-accent-blue),
        var(--color-secondary)
      );
      background-size: 300% 300%;
      animation: gradientShift var(--animation-very-slow) ease infinite;
    }

    &:hover {
      background: rgba(14, 165, 233, 0.3);
      transform: translateY(-1px);
    }
  }
}

/* Special buttons */
.orText {
  @include glass-effect(0.1);
  color: var(--text-primary);
  padding: 9px var(--spacing-lg);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  @include glass-border(0.1);
  box-shadow: 
    var(--shadow-md),
    var(--shadow-glass);
}

.removeBtn {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  background: rgba(220, 38, 38, 0.8);
  color: white;
  border: 1px solid rgba(220, 38, 38, 0.3);
  border-radius: var(--radius-full);
  width: 28px;
  height: 28px;
  cursor: pointer;
  font-size: var(--font-size-base);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-normal);
  @include glass-effect(0, 10px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);

  &:hover {
    background: rgba(220, 38, 38, 0.9);
    border-color: rgba(220, 38, 38, 0.5);
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(220, 38, 38, 0.4);
  }

  &:active {
    transform: scale(0.95);
  }

  @include focus-ring(#dc2626);
}

/* Dropdown button */
.dropdownBtn {
  composes: btnFormat;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  position: relative;

  .dropdownArrow {
    font-size: var(--font-size-sm);
    transition: transform var(--transition-normal);
    opacity: 0.7;
  }

  &.active .dropdownArrow,
  &[aria-expanded="true"] .dropdownArrow {
    transform: rotate(180deg);
    opacity: 1;
  }
}

/* Toggle button styles */
.toggleButton {
  composes: btn;
  @include glass-effect(0.03);
  @include glass-border(0.15);
  color: var(--text-tertiary);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
  box-shadow: 
    var(--shadow-sm),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);

  &::before {
    background: linear-gradient(
      90deg,
      rgba(139, 92, 246, 0.3),
      rgba(168, 85, 247, 0.3),
      rgba(139, 92, 246, 0.3)
    );
  }

  &:hover:not(:disabled) {
    border-color: rgba(255, 255, 255, 0.25);
    @include glass-effect(0.06);
    color: rgba(255, 255, 255, 0.95);
    transform: translateY(-1px);

    &::before {
      opacity: 1;
    }
  }

  &.active {
    background: rgba(139, 92, 246, 0.2);
    border-color: rgba(139, 92, 246, 0.4);
    color: var(--text-primary);
    font-weight: var(--font-weight-semibold);
    box-shadow: 
      0 4px 12px rgba(139, 92, 246, 0.3),
      var(--shadow-glass);

    &::before {
      opacity: 1;
      background: linear-gradient(
        90deg,
        rgba(139, 92, 246, 0.4),
        rgba(168, 85, 247, 0.4),
        rgba(139, 92, 246, 0.4)
      );
      background-size: 300% 300%;
      animation: gradientShift var(--animation-very-slow) ease infinite;
    }

    &:hover {
      background: rgba(139, 92, 246, 0.3);
      transform: translateY(-1px);
    }
  }
}

/* Assumption button styles */
.assumptionButton {
  composes: btn;
  @include glass-effect(0.03);
  @include glass-border(0.15);
  background: rgba(139, 92, 246, 0.1);
  border-color: rgba(139, 92, 246, 0.3);
  color: var(--text-secondary);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
  width: 100%;
  justify-content: flex-start;
  text-align: left;
  box-shadow: 
    var(--shadow-sm),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);

  &::before {
    background: linear-gradient(
      90deg,
      rgba(139, 92, 246, 0.3),
      rgba(168, 85, 247, 0.3),
      rgba(139, 92, 246, 0.3)
    );
  }

  &:hover:not(:disabled) {
    background: rgba(139, 92, 246, 0.2);
    border-color: rgba(139, 92, 246, 0.4);
    color: var(--text-primary);
    transform: translateY(-1px);

    &::before {
      opacity: 1;
    }
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    background: rgba(139, 92, 246, 0.15);
  }

  svg {
    color: rgba(139, 92, 246, 0.8);
  }

  span {
    flex: 1;
    line-height: var(--line-height-tight);
  }
}

/* Loading state */
.btnLoading {
  position: relative;
  color: transparent !important;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: var(--radius-full);
    animation: spin 1s linear infinite;
    z-index: 2;
  }

  &.btnPrimary::after {
    border-top-color: #000;
  }

  &.btnOutline::after {
    border-top-color: var(--text-tertiary);
  }
}

.spinner {
  animation: spin 5s linear infinite;
}

/* Animations */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 200% 50%; }
  75% { background-position: 300% 50%; }
}

/* Responsive adjustments */
@include responsive('tablet') {
  .actionButtons .btn {
    width: 100%;
    text-align: center;
    justify-content: center;
  }
  
  .btnFormat,
  .dropdownBtn {
    width: 100%;
    justify-content: center;
  }
  
  .btn {
    border-radius: var(--radius-sm);

    &::before {
      border-radius: var(--radius-sm);
    }
  }
}

@include responsive('mobile') {
  .btn {
    border-radius: var(--radius-sm);

    &::before {
      border-radius: var(--radius-sm);
    }
  }
  
  .orText {
    border-radius: var(--radius-sm);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .btn {
    border-width: 2px;
  }
  
  .btnPrimary {
    background: var(--color-primary);
    border-color: var(--color-primary);
    color: #000;
  }
  
  .btnOutline {
    @include glass-effect(0.1);
    border-color: rgba(255, 255, 255, 0.5);
    color: #fff;
  }
  
  .btnFormat {
    @include glass-effect(0.05);
    border-color: var(--border-secondary);
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .btn,
  .btn::before,
  .btn::after,
  .removeBtn,
  .dropdownArrow {
    transition: none;
    animation: none;
  }
  
  .btnPrimary::before,
  .btnFormat.active::before,
  .btnLoading::after,
  .spinner {
    animation: none;
  }
  
  .removeBtn:hover {
    transform: none;
  }
}
